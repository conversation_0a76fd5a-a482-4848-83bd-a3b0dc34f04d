<template>
	    <view class="container" :style="{ backgroundImage: `url('${getAssetUrl('/market-bg.png')}')` }">
		<CustomHead title="注册额度" />

		<!-- 1. 顶部tab切换 -->
		<view class="tab-container">
			<view class="tab-item" :class="{ active: activeTab === 'association' }" @click="switchTab('association')">
				协会债
			</view>
			<view class="tab-item" :class="{ active: activeTab === 'corporate' }" @click="switchTab('corporate')">
				公司债
			</view>
		</view>

		<!-- 搜索模块 -->
		<view class="search-container">
			<view class="search-box">
				<image class="search-icon" :src="getAssetUrl('/search-icon.svg')"
					mode="aspectFit"></image>
				<input class="search-input" type="text" placeholder="请输入关键词" v-model="keyWord"
					confirm-type="search" @confirm="handleSearch" />
			</view>
			<text class="cancel-text" @click="clearSearch">取消</text>
		</view>

		<!-- 筛选选项 -->
		<view class="filter-options">
			<view class="filter-item" @click="showProjectTypePicker">
				项目类型 <text class="arrow">▼</text>
			</view>
			<view class="filter-item" @click="showDateRangePicker">
				到期日期 <text class="arrow">▼</text>
			</view>
			<view class="filter-item" @click="showStatusPicker">
				状态 <text class="arrow">▼</text>
			</view>
		</view>

		<!-- 2. 卡片列表 -->
		<scroll-view scroll-y class="card-list" @scrolltolower="loadMore" @refresherpulling="onPulling"
			@refresherrefresh="onRefresh" @refresherrestore="onRestore" @refresherabort="onAbort"
			:refresher-enabled="true" :refresher-triggered="isRefreshing" :show-scrollbar="false">
			<!-- 无数据提示 -->
			<view class="no-data-container" v-if="noData">
				<text class="no-data-text">暂无数据</text>
			</view>

			<!-- 卡片内容 -->
			<view class="card-item" v-for="(item, index) in cardList" :key="index" v-else>
				<view class="card-header">
					<view class="card-title">{{ item.filenum || '--' }}</view>
					<view class="view-detail" @click="viewDetail(item)">查看占用明细 <text class="arrow-right">›</text></view>
				</view>

				<view class="card-info-row">
					<view class="card-info-col">
						<view class="card-amount number-font">{{ formatDecimal(item.sumAmount) || '0.0000' }}</view>
						<view class="card-label">累计使用额度(亿)</view>
					</view>
					<view class="card-info-col">
						<view class="card-amount number-font">{{ formatDecimal(item.occupyAmount) || '0.0000' }}</view>
						<view class="card-label">占用额度(亿)</view>
					</view>
					<view class="card-info-col">
						<view class="card-amount number-font">{{ formatDecimal(item.remainingAmount) || '0.0000' }}</view>
						<view class="card-label">剩余额度(亿)</view>
					</view>
				</view>

				<view class="card-detail-table">
					<view class="card-detail-row">
						<view class="card-detail-label">初始注册额度(亿)</view>
						<view class="card-detail-value number-font">{{ formatDecimal(item.intAmount) || '0.0000' }}</view>
					</view>
					<view class="card-detail-row">
						<view class="card-detail-label">有效注册额度(亿)</view>
						<view class="card-detail-value number-font">{{ formatDecimal(item.amount) || '0.0000' }}</view>
					</view>
					<view class="card-detail-row">
						<view class="card-detail-label">项目类型</view>
						<view class="card-detail-value">{{ item.bondtype || '(未知)' }}</view>
					</view>
					<view class="card-detail-row">
						<view class="card-detail-label">注册日期</view>
						<view class="card-detail-value number-font">{{ item.meetingDt || '--' }}</view>
					</view>
					<view class="card-detail-row">
						<view class="card-detail-label">到期日期</view>
						<view class="card-detail-value number-font">{{ item.endDtEffective || '--' }}</view>
					</view>
					<view class="card-detail-row">
						<view class="card-detail-label">主承销商</view>
						<view class="card-detail-value">{{ item.leadunderwriter || '--' }}</view>
					</view>
				</view>
			</view>

			<!-- 加载更多提示 -->
			<view class="loading-more" v-if="isLoadingMore && !noData">加载中...</view>
			<view class="no-more" v-if="noMoreData && !noData">没有更多数据了</view>
		</scroll-view>

		<!-- 项目类型选择弹窗 -->
		<uni-popup ref="projectTypePopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">项目类型选择</text>
					<view class="close-icon" @click="closeProjectTypePicker">
						<uni-icons type="close" size="20" color="#333"></uni-icons>
					</view>
				</view>
				
				<view class="popup-options">
					<view class="options-header">
						<text>默认勾选全部</text>
						<view class="options-actions">
							<text @click="selectAllProjectTypes">全选</text>
							<text @click="clearProjectTypes">清空</text>
							<text @click="invertProjectTypes">反选</text>
						</view>
					</view>
					
					<view class="options-list">
						<view 
							v-for="(option, index) in projectTypeOptions" 
							:key="index" 
							class="option-item"
							@click="toggleProjectType(option)"
						>
							<view class="checkbox" :class="{ checked: selectedProjectTypes.includes(option.value) }">
								<uni-icons v-if="selectedProjectTypes.includes(option.value)" type="checkmarkempty" size="14" color="#fff"></uni-icons>
							</view>
							<text class="option-label">{{ option.label }}</text>
						</view>
					</view>
				</view>
				
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmProjectTypeSelection">确认</button>
				</view>
			</view>
		</uni-popup>
		
		<!-- 状态选择弹窗 -->
		<uni-popup ref="statusPopup" type="bottom" :safe-area="false">
			<view class="popup-content">
				<view class="popup-header">
					<text class="popup-title">状态选择</text>
					<view class="close-icon" @click="closeStatusPicker">
						<uni-icons type="close" size="20" color="#333"></uni-icons>
					</view>
				</view>
				
				<view class="popup-options">
					<view class="status-options-list">
						<view 
							v-for="(option, index) in statusOptions" 
							:key="index" 
							class="status-option-item"
							@click="selectStatus(option.value)"
						>
							<view class="radio" :class="{ checked: currentStatus === option.value }">
								<view class="radio-inner" v-if="currentStatus === option.value"></view>
							</view>
							<text class="option-label">{{ option.label }}</text>
						</view>
					</view>
				</view>
				
				<view class="popup-footer">
					<button class="confirm-btn" @click="confirmStatusSelection">确认</button>
				</view>
			</view>
		</uni-popup>
		
		<!-- 日期范围选择 -->
		<DateRangePicker 
			:visible="dateRangeVisible" 
			@dateRangeChange="handleDateRangeChange" 
			@update:visible="updateDateRangeVisible"
		/>
	</view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import CustomHead from '@/components/head/head.vue';
import { getRegisterQuotaList } from '@/api/registerAbove';
import DateRangePicker from '@/components/calendar/DateRangePicker.vue';
import { getAssetUrl } from '@/config/assets';

// 默认协会债
const ccid = ref("a253d521b12440609efa6bab8f71cb16")
// 项目类型
const projectType = ref([])
// 是否含已到期
const isExpired = ref(0)
// 开始日期
const startDate = ref("")
// 结束日期
const endDate = ref("")

// 日期选择器相关
const dateRangeVisible = ref(false);

// 显示日期范围选择器
const showDateRangePicker = () => {
	dateRangeVisible.value = true;
};

// 更新日期范围选择器可见性
const updateDateRangeVisible = (visible) => {
	dateRangeVisible.value = visible;
};

// 处理日期范围变更
const handleDateRangeChange = (dateRange) => {
	console.log('选择的日期范围:', dateRange);
	startDate.value = dateRange[0];
	endDate.value = dateRange[1];
	
	// 筛选数据
	resetCardList();
	getRegisterQuotaData();
};
// 格式化数值方法 - 保留小数点后四位
const formatDecimal = (value) => {
	// 如果值为null、undefined或空字符串，返回默认值
	if (value === null || value === undefined || value === '') {
		return '0.0000';
	}
	
	// 如果是字符串类型，先去除前后空格
	if (typeof value === 'string') {
		value = value.trim();
		// 如果去除空格后为空字符串，返回默认值
		if (value === '') {
			return '0.0000';
		}
	}
	
	// 转换为数字类型
	const num = Number(value);
	
	// 如果转换失败（NaN），返回默认值
	if (isNaN(num)) {
		return '0.0000';
	}
	
	// 保留四位小数，不足时自动补0
	return num.toFixed(4);
};
const getRegisterQuotaData = () => {
    uni.showLoading({
        title: '加载中',
        mask: true
    })
	const data = {
		params: {
			ownedModuleid: "1362426695289679872",
			ccid: ccid.value,
			companyCode: "",
			isExpired: isExpired.value,
			projectType: projectType.value,
			keyWord: keyWord.value,
			startDate: startDate.value,
			endDate: endDate.value,
		},
		page: {
			pageNo: pageNo.value,
			pageSize: pageSize.value,
			// sort: null,
			// direction: null
		}
	}
	getRegisterQuotaList(data).then(res => {
		console.log(res);
		if (res.data?.data?.pageInfo?.list?.length > 0) {
			if (pageNo.value === 1) {
				// 第一页数据直接替换
				cardList.value = res.data?.data?.pageInfo?.list;
			} else {
				// 加载更多时追加数据
				cardList.value = [...cardList.value, ...res.data?.data?.pageInfo?.list];
			}
			console.log(cardList.value);
			
			// 判断是否还有下一页
			if (res.data?.data?.pageInfo?.nextPage === 0) {
				noMoreData.value = true;
				isLoadingMore.value = false;
			} else {
				noMoreData.value = false;
				isLoadingMore.value = false;
			}
		} else {
			// 没有更多数据了
			isLoadingMore.value = false;
			if (pageNo.value === 1) {
				noData.value = true;
			} else {
				noMoreData.value = true;
			}
		}

		isRefreshing.value = false;
		uni.hideLoading();
	})
}
// 搜索相关
const keyWord = ref('');

// 处理搜索
const handleSearch = () => {
	if (keyWord.value.trim()) {
		// 实现搜索逻辑
		resetCardList();
		getRegisterQuotaData();
	}
};

// 清除搜索
const clearSearch = () => {
	keyWord.value = '';
	resetCardList();
	getRegisterQuotaData();
};

// 当前激活的标签页
const activeTab = ref('association'); // 默认显示公司债

// 切换标签页
const switchTab = (tab) => {
	activeTab.value = tab;
	ccid.value = tab === 'association' ? "a253d521b12440609efa6bab8f71cb16" : "6d4e0105257344f4b2307a910fffef48"
	
	// 根据标签切换项目类型选项
	projectTypeOptions.value = tab === 'association' 
		? [
            { label: 'SCP', value: '4' },
			{ label: 'CP', value: '5' },
			{ label: 'MTN', value: '6' },
			{ label: 'PPN', value: '18' },
			{ label: '集合票据', value: '15' },
			{ label: 'ABN', value: '9' },
			{ label: 'PRN', value: '10' },
			{ label: 'TDFI', value: '17' },
			{ label: 'DFI', value: '7' },
			{ label: 'CB', value: '19' }
		] 
		: [
			{ label: '小公募', value: '1' },
			{ label: '私募', value: '2' },
			{ label: 'ABS', value: '3' }
		];
	
	// 清空已选择的项目类型
	selectedProjectTypes.value = [];
	// 重置projectType为空数组
	projectType.value = [];
	// 重置状态为未到期
	currentStatus.value = 0;
	isExpired.value = 0;
	// 重置日期范围
	startDate.value = "";
	endDate.value = "";
	
	// 切换标签时重新加载数据
	resetCardList();
	getRegisterQuotaData();
};

// 卡片列表数据
const cardList = ref([]);
const pageNo = ref(1);
const pageSize = ref(10);
const noMoreData = ref(false);
const isRefreshing = ref(false);
const isLoadingMore = ref(false);
const noData = ref(false); // 无数据标志
const isDataReady = ref(true); // 数据准备标志

// 重置列表
const resetCardList = () => {
	cardList.value = [];
	pageNo.value = 1;
	noMoreData.value = false;
	noData.value = false;
};

// 上拉加载更多
const loadMore = () => {
	if (noMoreData.value || isLoadingMore.value) return;
	console.log('上拉加载更多...');
	isLoadingMore.value = true;
	
	// 增加页码
	pageNo.value += 1;
	getRegisterQuotaData();
};

// 下拉刷新相关函数
const onPulling = () => {
	console.log('下拉刷新触发中...');
};

const onRefresh = () => {
	console.log('下拉刷新触发');
	isRefreshing.value = true;
	resetCardList();
	getRegisterQuotaData();
};

const onRestore = () => {
	console.log('下拉刷新恢复');
};

const onAbort = () => {
	console.log('下拉刷新中止');
};

// 页面加载时获取数据
onMounted(() => {
	// 默认加载数据
	getRegisterQuotaData();
});

// 查看占用明细
const viewDetail = (item) => {
	uni.navigateTo({
		url: `/subPageA/register-quota-detail/register-quota-detail?filenum=${item.filenum}`
	});
}

// 项目类型弹窗相关
const projectTypePopup = ref(null);
const selectedProjectTypes = ref([]);

// 项目类型协会债选项码值
const projectTypeOptions = ref([
	{ label: 'CP', value: '5' },
	{ label: 'SCP', value: '4' },
	{ label: 'MTN', value: '6' },
	{ label: 'PPN', value: '18' },
	{ label: '集合票据', value: '15' },
	{ label: 'ABN', value: '9' },
	{ label: 'PRN', value: '10' },
	{ label: 'TDFI', value: '17' },
	{ label: 'DFI', value: '7' },
	{ label: 'CB', value: '19' }
]);

// 项目类型公司债选项码值
const corporateProjectTypeOptions = ref([
	{ label: '小公募', value: '1' },
	{ label: '私募', value: '2' },
	{ label: 'ABS', value: '3' }
]);

// 显示项目类型选择器
const showProjectTypePicker = () => {
	projectTypePopup.value.open();
};

// 关闭项目类型选择器
const closeProjectTypePicker = () => {
	projectTypePopup.value.close();
};

// 切换项目类型选中状态
const toggleProjectType = (option) => {
	const index = selectedProjectTypes.value.indexOf(option.value);
	if (index > -1) {
		selectedProjectTypes.value.splice(index, 1);
	} else {
		selectedProjectTypes.value.push(option.value);
	}
};

// 全选项目类型
const selectAllProjectTypes = () => {
	selectedProjectTypes.value = projectTypeOptions.value.map(option => option.value);
};

// 清空项目类型选择
const clearProjectTypes = () => {
	selectedProjectTypes.value = [];
};

// 反选项目类型
const invertProjectTypes = () => {
	const allValues = projectTypeOptions.value.map(option => option.value);
	selectedProjectTypes.value = allValues.filter(value => !selectedProjectTypes.value.includes(value));
};

// 确认项目类型选择
const confirmProjectTypeSelection = () => {
	console.log('已选择的项目类型:', selectedProjectTypes.value);
	// 将选中的项目类型数组直接赋值给projectType
	projectType.value = [...selectedProjectTypes.value];
	closeProjectTypePicker();
	// 筛选数据
	resetCardList();
	getRegisterQuotaData();
};

// 状态弹窗相关
const statusPopup = ref(null);
const currentStatus = ref(0); // 默认未到期

// 状态选项
const statusOptions = [
	{ label: '未到期', value: 0 },
	{ label: '含已到期', value: 1 }
];

// 显示状态选择器
const showStatusPicker = () => {
	statusPopup.value.open();
};

// 关闭状态选择器
const closeStatusPicker = () => {
	statusPopup.value.close();
};

// 选择状态
const selectStatus = (value) => {
	currentStatus.value = value;
};

// 确认状态选择
const confirmStatusSelection = () => {
	console.log('已选择的状态:', currentStatus.value);
	isExpired.value = currentStatus.value;
	closeStatusPicker();
	// 筛选数据
	resetCardList();
	getRegisterQuotaData();
};
</script>

<style lang="scss" scoped>
.container {
	padding: 0 20rpx;
	background: linear-gradient(to bottom, #ffeac3, #fff);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	height: 100vh;
	display: flex;
	flex-direction: column;
}


// 1. 顶部tab切换样式
.tab-container {
	display: flex;
	margin-bottom: 20rpx;
	border-radius: 10rpx;

	.tab-item {
		flex: 1;
		padding: 30rpx 0;
		text-align: center;
		position: relative;
		font-size: 32rpx;
		color: #999;
		transition: all 0.3s ease;

		&.active {
			color: #FF9900;
			font-weight: bold;

			&::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 40%;
				height: 6rpx;
				background-color: #FF9900;
				border-radius: 6rpx 6rpx 0 0;
			}
		}
	}
}

// 搜索模块样式
.search-container {
	display: flex;
	align-items: center;
	padding: 0 10rpx 20rpx;
}

.search-box {
	flex: 1;
	display: flex;
	align-items: center;
	background-color: #ffffff;
	border-radius: 36rpx;
	padding: 0 20rpx;
	height: 72rpx;
}

.search-icon {
	width: 36rpx;
	height: 36rpx;
	margin-right: 10rpx;
}

.search-input {
	flex: 1;
	height: 100%;
	font-size: 28rpx;
}

.cancel-text {
	font-size: 28rpx;
	color: #666;
	padding: 0 20rpx;
}

// 筛选选项样式
.filter-options {
	display: flex;
	justify-content: space-between;
	padding: 20rpx;
	background-color: #fff;
	border-radius: 12rpx 12rpx 0 0;

	.filter-item {
		display: flex;
		align-items: center;
		padding: 16rpx 30rpx;
		border-radius: 8rpx;
		font-size: 28rpx;
		color: #333333;
		
		.arrow {
			margin-left: 8rpx;
			font-size: 22rpx;
			color: #000;
		}
		
		&:active {
			opacity: 0.8;
		}
	}
}

// 2. 卡片列表样式
.card-list {
	flex: 1;
	height: 0;

	/* 隐藏滚动条 */
	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE and Edge */

	/* WebKit浏览器 */
	&::-webkit-scrollbar {
		display: none;
		width: 0;
		height: 0;
	}
}

/* 针对小程序平台的强化处理 */
/* #ifdef MP-WEIXIN */
.card-list {
	-webkit-overflow-scrolling: touch;
}

.card-list ::-webkit-scrollbar {
	display: none;
}

/* #endif */

.card-item {
	background-color: #fff;
	border-radius: 0 0 12rpx 12rpx;
	padding: 30rpx;
	margin-bottom: 20rpx;
	box-shadow: 0rpx 14rpx 28rpx 0rpx rgba(219, 219, 219, 0.48);
}

.card-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.card-title {
	font-size: 32rpx;
	font-weight: bold;
	line-height: 1.4;
}

.view-detail {
	font-size: 28rpx;
	color: #FF9900;
}

.arrow-right {
	color: #FF9900;
	font-size: 32rpx;
}

.card-info-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 30rpx;
	padding-bottom: 20rpx;
	border-bottom: 1px solid #f0f0f0;
}

.card-info-col {
	display: flex;
	flex-direction: column;
}

.card-amount {
	font-size: 40rpx;
	font-weight: bold;
	color: #333;
	line-height: 1.2;
}

.card-label {
	font-size: 24rpx;
	color: #999;
	margin-top: 10rpx;
}

.card-detail-table {
	padding-top: 20rpx;
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.card-detail-row {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.card-detail-label {
	color: #999;
	font-size: 28rpx;
}

.card-detail-value {
	color: #333;
    max-width:400rpx;
	font-size: 28rpx;
	text-align: right;
	font-weight: 500;
}

// 加载提示样式
.loading-more,
.no-more {
	text-align: center;
	padding: 20rpx 0;
	color: #999;
	font-size: 26rpx;
}

// 无数据样式
.no-data-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 100rpx 0;
}

.no-data-image {
	width: 200rpx;
	height: 200rpx;
	margin-bottom: 20rpx;
}

.no-data-text {
	font-size: 28rpx;
	color: #999;
}

/* 弹窗样式 */
.popup-content {
	background-color: #fff;
	border-radius: 24rpx 24rpx 0 0;
	padding: 30rpx;
	max-height: 70vh;
	display: flex;
	flex-direction: column;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-bottom: 30rpx;
	border-bottom: 2rpx solid #F5F5F5;
	
	.popup-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
	}
	
	.close-icon {
		padding: 10rpx;
	}
}

.popup-options {
	flex: 1;
	overflow: hidden;
	padding: 20rpx 0;
	max-height: 50vh;
	
	.options-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;
		
		text {
			font-size: 26rpx;
			color: #999;
		}
		
		.options-actions {
			display: flex;
			gap: 30rpx;
			
			text {
				color: #FF9900;
				font-size: 26rpx;
				
				&:active {
					opacity: 0.8;
				}
			}
		}
	}
	
	.options-list {
		display: flex;
		flex-wrap: wrap;
		padding: 20rpx 0;
		max-height: 40vh;
		overflow-y: auto;
		
		.option-item {
			width: 50%;
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			
			.checkbox {
				width: 36rpx;
				height: 36rpx;
				border-radius: 6rpx;
				border: 2rpx solid #DCDFE6;
				margin-right: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s;
				
				&.checked {
					background-color: #FF9900;
					border-color: #FF9900;
				}
			}
			
			.option-label {
				font-size: 28rpx;
				color: #333;
			}
		}
	}
	
	/* 状态选择样式 */
	.status-options-list {
		padding: 20rpx 0;
		max-height: 40vh;
		overflow-y: auto;
		
		.status-option-item {
			display: flex;
			align-items: center;
			padding: 30rpx 0;
			
			.radio {
				width: 36rpx;
				height: 36rpx;
				border-radius: 50%;
				border: 2rpx solid #DCDFE6;
				margin-right: 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s;
				
				&.checked {
					border-color: #FF9900;
				}
				
				.radio-inner {
					width: 20rpx;
					height: 20rpx;
					border-radius: 50%;
					background-color: #FF9900;
				}
			}
			
			.option-label {
				font-size: 28rpx;
				color: #333;
			}
		}
	}
}

.popup-footer {
	padding-top: 30rpx;
	border-top: 2rpx solid #F5F5F5;
	
	.confirm-btn {
		width: 100%;
		height: 88rpx;
		background: #FF9900;
		border-radius: 44rpx;
		color: #FFFFFF;
		font-size: 32rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		border: none;
		
		&:active {
			opacity: 0.9;
		}
	}
}

/* 滚动条样式 */
::-webkit-scrollbar {
	width: 0;
	height: 0;
	color: transparent;
}
</style>