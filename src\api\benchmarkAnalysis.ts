import request from '@/utils/request'

// 对标API前缀
const APIPRE_BOND = '/benchmarking/benchmarkingConfig/'
// 查询获取对标分析标签信息
export const getBenchmarkAnalysisTab = () => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingGroup`,
    })
}

// 查询获取已配置的对标企业信息
export const getBenchmarkCompanyConfigList = (data: object) => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingRelation`,
        data
    })
}

// 查询获取对标企业信息添加-添加企业页面
export const getBenchmarkCompanyList = (data: object) => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingCompany`,
        data
    })
}



// 查询获取已配置的对标企业下拉选择信息
// 获取到的data格式
// [
//     {
//         "belongPlatform": null,
//         "bmCompSname": null,
//         "bmEntId": "617bitmI4f",
//         "bmEntName": "安徽兆尹信息科技股份有限公司",
//         "bmEntOrgCode": "*********",
//         "entId": "617bitmI4f",
//         "entName": "安徽兆尹信息科技股份有限公司",
//         "entOrgCode": "*********",
//         "groupId": "my_benchmarking_group",
//         "id": "6B89640DC7214881B940C4EEC6E96553",
//         "indexSort": null,
//         "tmCreater": null,
//         "tmCreatetime": null,
//         "tmEffectflag": "E",
//         "tmOrgid": null,
//         "tmUpdater": null,
//         "tmUpdatetime": null
//     },
// ]
// 传参data格式
// {
//     "entId": "617bitmI4f",
//     "entOrgCode": "*********",
//     "groupId": "my_benchmarking_group"
// }
export const getBenchmarkCompanySelectList = (data: object) => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingRelationSelect`,
        data
    })
}



// 获取债券类型
// 获取到的data格式
// [
//     {
//         "bondTypeCode": "a101020700",
//         "bondTypeName": "企业债",
//         "bondTypeShortName2": "",
//         "list": [
//             {
//                 "bondTypeCode": "1000004568",
//                 "bondTypeName": "一般企业债",
//                 "bondTypeShortName2": "一般企业债",
//                 "list": [],
//                 "parentBondTypeCode": "a101020700"
//             },
//             {
//                 "bondTypeCode": "1000004569",
//                 "bondTypeName": "集合企业债",
//                 "bondTypeShortName2": "集合企业债",
//                 "list": [],
//                 "parentBondTypeCode": "a101020700"
//             }
//         ],
//         "parentBondTypeCode": "aaa"
//     },
//     {
//         "bondTypeCode": "a101020800",
//         "bondTypeName": "公司债",
//         "bondTypeShortName2": "",
//         "list": [
//             {
//                 "bondTypeCode": "1000007923",
//                 "bondTypeName": "企业资产支持证券",
//                 "bondTypeShortName2": "ABS",
//                 "list": [],
//                 "parentBondTypeCode": "a101020800"
//             },
//             {
//                 "bondTypeCode": "1000009966",
//                 "bondTypeName": "一般公司债",
//                 "bondTypeShortName2": "一般公司债",
//                 "list": [],
//                 "parentBondTypeCode": "a101020800"
//             },
//             {
//                 "bondTypeCode": "1000009967",
//                 "bondTypeName": "私募债",
//                 "bondTypeShortName2": "私募债",
//                 "list": [],
//                 "parentBondTypeCode": "a101020800"
//             }
//         ],
//         "parentBondTypeCode": "aaa"
//     },
//     {
//         "bondTypeCode": "a101021000",
//         "bondTypeName": "协会债",
//         "bondTypeShortName2": "",
//         "list": [
//             {
//                 "bondTypeCode": "1000004194",
//                 "bondTypeName": "超短期融资券",
//                 "bondTypeShortName2": "SCP",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000004195",
//                 "bondTypeName": "集合票据",
//                 "bondTypeShortName2": "集合票据",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000004566",
//                 "bondTypeName": "短期融资券",
//                 "bondTypeShortName2": "CP",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000004570",
//                 "bondTypeName": "中期票据",
//                 "bondTypeShortName2": "MTN",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000007926",
//                 "bondTypeName": "资产支持票据",
//                 "bondTypeShortName2": "ABN",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000013981",
//                 "bondTypeName": "定向工具",
//                 "bondTypeShortName2": "PPN",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             },
//             {
//                 "bondTypeCode": "1000028211",
//                 "bondTypeName": "项目收益票据",
//                 "bondTypeShortName2": "PRN",
//                 "list": [],
//                 "parentBondTypeCode": "a101021000"
//             }
//         ],
//         "parentBondTypeCode": "aaa"
//     }
// ]
export const getBondType = () => {
    return request({
        method: 'POST',
        url: '/bond/bondDescription/queryDictBondType',
    })
}

// 获取报告类型
// 获取到的data格式
//[]
export const getReportType = () => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingAssetReportYear`,
    })
}

// 查询获取财务对标报告期下拉选择信息
// 获取到的data格式
// [
//     {
//       "list": [
//         {
//           "reportCode": "01",
//           "reportName": "一季度",
//           "reportYear": "2023"
//         },
//         {
//           "reportCode": "02",
//           "reportName": "半年度",
//           "reportYear": "2023"
//         },
//         {
//           "reportCode": "03",
//           "reportName": "三季度",
//           "reportYear": "2023"
//         },
//         {
//           "reportCode": "04",
//           "reportName": "年报",
//           "reportYear": "2023"
//         }
//       ],
//       "reportCode": "2023",
//       "reportName": "2023年",
//       "reportYear": "2023"
//     },
//     {
//       "list": [
//         {
//           "reportCode": "01",
//           "reportName": "一季度",
//           "reportYear": "2024"
//         },
//         {
//           "reportCode": "02",
//           "reportName": "半年度",
//           "reportYear": "2024"
//         },
//         {
//           "reportCode": "03",
//           "reportName": "三季度",
//           "reportYear": "2024"
//         },
//         {
//           "reportCode": "04",
//           "reportName": "年报",
//           "reportYear": "2024"
//         }
//       ],
//       "reportCode": "2024",
//       "reportName": "2024年",
//       "reportYear": "2024"
//     },
//     {
//       "list": [
//         {
//           "reportCode": "01",
//           "reportName": "一季度",
//           "reportYear": "2025"
//         }
//       ],
//       "reportCode": "2025",
//       "reportName": "2025年",
//       "reportYear": "2025"
//     }
//   ]
// 传参data格式,空
export const getFinancialBenchmarkReportPeriod = () => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingAssetReportYear`,
    })
}

// 财务对标-趋势分析-指标选择
// 传参
// {
//     "entOrgCode": "*********",
//     "moduleType": "FINANCIAL_BENCHMARKING"
// }
export const getFinancialIndicatorsList = (data: object) => {
    return request({
        method: 'POST',
        url: `${APIPRE_BOND}getBenchmarkingAssetSetting`,
        data
    })
}

